import { useInfiniteCategories } from './useInfiniteCategories';

export const useGetCategories = () => {
  const {
    data,
    isLoading,
    error,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
  } = useInfiniteCategories({
    only_with_deals: true,
    limit: 20,
  });

  return {
    data: data.length > 0 ? data : null,
    isLoading,
    error,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
  };
};
