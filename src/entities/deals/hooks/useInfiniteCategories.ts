import { fetcher } from '@lib/fetcher';
import { useCallback, useEffect, useMemo, useState } from 'react';

export interface DealCategory {
  name: string;
}

export interface DealCategoryPagination {
  data: DealCategory[];
  total: number;
  per_page: number;
  current_page: number;
  from: number | null;
  to: number | null;
  last_page: number;
  has_more_pages: boolean;
}

export interface DealsCategoriesQueryResponse {
  deals_categories: DealCategoryPagination;
}

export interface UseInfiniteCategoriesOptions {
  only_with_deals?: boolean;
  limit?: number;
  enabled?: boolean;
}

export interface UseInfiniteCategoriesResult {
  data: DealCategory[];
  isLoading: boolean;
  isFetchingNextPage: boolean;
  hasNextPage: boolean;
  error: Error | null;
  fetchNextPage: () => void;
  currentPage: number;
  total: number;
}

export const useInfiniteCategories = (
  options: UseInfiniteCategoriesOptions = {},
): UseInfiniteCategoriesResult => {
  const { only_with_deals = true, limit = 20, enabled = true } = options;

  const [currentPage, setCurrentPage] = useState(1);
  const [allCategories, setAllCategories] = useState<DealCategory[]>([]);
  const [isFetchingNextPage, setIsFetchingNextPage] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [paginationData, setPaginationData] =
    useState<DealCategoryPagination | null>(null);

  const queryVariables = useMemo(
    () => ({
      limit,
      page: currentPage,
      only_with_deals,
    }),
    [limit, currentPage, only_with_deals],
  );

  const fetchCategories = useCallback(async () => {
    if (!enabled) return;

    try {
      if (currentPage === 1) {
        setIsLoading(true);
      } else {
        setIsFetchingNextPage(true);
      }

      setError(null);

      const query = `
        query DealsCategoriesPaginated($limit: Int, $page: Int, $only_with_deals: Boolean) {
          deals_categories(limit: $limit, page: $page, only_with_deals: $only_with_deals) {
            data {
              name
            }
            total
            per_page
            current_page
            from
            to
            last_page
            has_more_pages
          }
        }
      `;

      const fetchData = fetcher<
        DealsCategoriesQueryResponse,
        typeof queryVariables
      >(query, queryVariables);
      const data = await fetchData();

      if (data?.deals_categories) {
        const { deals_categories } = data;
        setPaginationData(deals_categories);

        if (currentPage === 1) {
          setAllCategories(deals_categories.data || []);
        } else {
          setAllCategories((prev) => [
            ...prev,
            ...(deals_categories.data || []),
          ]);
        }
      }
    } catch (err) {
      setError(
        err instanceof Error ? err : new Error('Failed to fetch categories'),
      );
    } finally {
      setIsLoading(false);
      setIsFetchingNextPage(false);
    }
  }, [enabled, currentPage, queryVariables]);

  useEffect(() => {
    fetchCategories();
  }, [fetchCategories]);

  const hasNextPage = paginationData?.has_more_pages ?? false;
  const total = paginationData?.total ?? 0;

  const fetchNextPage = useCallback(() => {
    if (hasNextPage && !isFetchingNextPage && !isLoading) {
      setCurrentPage((prev) => prev + 1);
    }
  }, [hasNextPage, isFetchingNextPage, isLoading]);

  // Reset when options change
  const optionsKey = JSON.stringify({ only_with_deals, limit, enabled });
  useEffect(() => {
    setCurrentPage(1);
    setAllCategories([]);
    setIsFetchingNextPage(false);
    setPaginationData(null);
  }, [optionsKey]);

  return {
    data: allCategories,
    isLoading,
    isFetchingNextPage,
    hasNextPage,
    error,
    fetchNextPage,
    currentPage,
    total,
  };
};
