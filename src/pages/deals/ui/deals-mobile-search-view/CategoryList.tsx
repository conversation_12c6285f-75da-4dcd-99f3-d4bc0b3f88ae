import { Typography } from '@components/typography';
import { LOCIZE_DEALS_KEYS, LOCIZE_NAMESPACES } from '@config/locize';
import { useInfiniteCategories } from '@entities/deals/hooks/useInfiniteCategories';
import { useInfiniteScroll } from '@hooks/system/useInfiniteScroll';
import { getRouteApi } from '@tanstack/react-router';
import { useTranslation } from 'react-i18next';

import {
  getDealCategoryIcon,
  getDealCategoryTranslationKey,
  isDealCategory,
} from '../../config';
import type { DealCategoryName } from '../../types';

const routeApi = getRouteApi('/_protected/_main/deals');

export const CategoryList = () => {
  const {
    data: categories,
    isLoading,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
  } = useInfiniteCategories({
    only_with_deals: true,
    limit: 20,
  });
  const navigate = routeApi.useNavigate();
  const { t } = useTranslation(LOCIZE_NAMESPACES.deals);

  const { triggerRef } = useInfiniteScroll(fetchNextPage, {
    enabled: !isLoading,
    hasNextPage,
    isFetching: isFetchingNextPage,
    rootMargin: '100px',
  });

  const onCategoryClick = (category: DealCategoryName) => {
    navigate({
      search: {
        category,
      },
    });
  };

  if (!categories || categories.length === 0) {
    return null;
  }

  return (
    <div className="flex h-[calc(100vh-10rem)] flex-col">
      <Typography className="mb-4" variant="text-m" affects="semibold">
        {t(LOCIZE_DEALS_KEYS.dealsCategories)}
      </Typography>
      <div className="no-scrollbar h-full min-h-0 flex-1 overflow-y-auto">
        {categories.map((category) => {
          if (!category?.name) {
            return null;
          }

          if (!isDealCategory(category.name)) {
            return null;
          }

          const categoryName = category.name;
          const Icon = getDealCategoryIcon(categoryName);
          const translationKey = getDealCategoryTranslationKey(categoryName);

          return (
            <button
              type="button"
              onClick={() => onCategoryClick(categoryName)}
              key={category.name}
              className="flex w-full items-center gap-2 border-gray-200 border-b py-[0.75rem] hover:bg-gray-50 "
            >
              <Icon className="mr-2 h-5 w-5 shrink-0" />
              <Typography variant="text-m">{t(translationKey)}</Typography>
            </button>
          );
        })}

        {hasNextPage && (
          <div
            ref={triggerRef}
            className="h-4 w-full"
            style={{
              minHeight: '20px',
              visibility: 'visible',
              display: 'block',
            }}
            data-testid="infinite-scroll-trigger"
          />
        )}
      </div>
    </div>
  );
};
