import { Skeleton } from '@components/ui/skeleton';
import { LOCIZE_NAMESPACES } from '@config/locize';
import { useInfiniteCategories } from '@entities/deals/hooks/useInfiniteCategories';
import { useInfiniteScroll } from '@hooks/system/useInfiniteScroll';
import {
  getDealCategoryTranslationKey,
  isDealCategory,
} from '@pages/deals/config';
import { Suspense } from 'react';
import { useTranslation } from 'react-i18next';

import { DealsCategoryMainSectionCarousel } from './DealsCategoryMainSectionCarousel';
import FeaturedDealsList from './FeaturedDealsList';

const DealMainSection = () => {
  const {
    data: categories,
    isLoading,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
  } = useInfiniteCategories();
  const { t } = useTranslation(LOCIZE_NAMESPACES.deals);

  const { triggerRef } = useInfiniteScroll(fetchNextPage, {
    enabled: !isLoading,
    hasNextPage,
    isFetching: isFetchingNextPage,
    rootMargin: '200px',
  });

  if (!categories || categories.length === 0) {
    return null;
  }

  return (
    <div className={'mt-[2.5rem]'}>
      <FeaturedDealsList />
      {categories?.map((category) => {
        if (!category) {
          return null;
        }

        if (!isDealCategory(category.name)) {
          return null;
        }

        const categoryName = category.name;
        const translationKey = getDealCategoryTranslationKey(categoryName);
        const title = t(translationKey);

        return (
          <Suspense
            key={categoryName}
            fallback={
              <Skeleton className="mx-6 mb-10 h-[24rem] rounded-2xl md:mx-12" />
            }
          >
            <DealsCategoryMainSectionCarousel
              category={categoryName}
              title={title}
            />
          </Suspense>
        );
      })}

      {hasNextPage && (
        <div
          ref={triggerRef}
          className="h-4 w-full"
          style={{
            minHeight: '20px',
            visibility: 'visible',
            display: 'block',
          }}
          data-testid="infinite-scroll-trigger"
        />
      )}
    </div>
  );
};

export default DealMainSection;
