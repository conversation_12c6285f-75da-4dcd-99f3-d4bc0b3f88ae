import { Button } from '@components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@components/ui/dropdown-menu';
import { LOCIZE_DEALS_KEYS, LOCIZE_NAMESPACES } from '@config/locize';
import { useInfiniteCategories } from '@entities/deals/hooks/useInfiniteCategories';
import { useInfiniteScroll } from '@hooks/system/useInfiniteScroll';
import { getRouteApi } from '@tanstack/react-router';
import { cn } from '@utils/tailwind';
import { Check, Menu, Tag } from 'lucide-react';
import * as React from 'react';
import { useTranslation } from 'react-i18next';

import {
  getDealCategoryIcon,
  getDealCategoryTranslationKey,
  isDealCategory,
} from '../config';
import type { NullableDealCategoryValue } from '../types';

type DealsCategoriesSelectDesktopProps = {
  onSelect: (value: NullableDealCategoryValue) => void;
  value: NullableDealCategoryValue;
};
const routeApi = getRouteApi('/_protected/_main/deals');

export const DealsCategoriesSelectDesktop = ({
  onSelect,
  value,
}: DealsCategoriesSelectDesktopProps) => {
  const {
    data: categories,
    isLoading,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
  } = useInfiniteCategories({
    only_with_deals: true,
    limit: 10,
  });
  const [open, setOpen] = React.useState(false);
  const { title } = routeApi.useSearch();

  const { triggerRef } = useInfiniteScroll(fetchNextPage, {
    enabled: !isLoading,
    hasNextPage,
    isFetching: isFetchingNextPage,
    rootMargin: '100px',
  });

  const { t } = useTranslation(LOCIZE_NAMESPACES.deals);

  if (!categories || categories.length === 0) {
    return null;
  }

  const selectCategory = (value: NullableDealCategoryValue) => {
    setOpen(false);
    onSelect(value);
  };

  return (
    <DropdownMenu open={open} onOpenChange={setOpen}>
      <DropdownMenuTrigger asChild>
        <Button
          variant="outline"
          className={cn(
            'w-full justify-between rounded-full border px-6 py-6 md:w-fit',
            open || value ? 'border-black' : 'border-color',
          )}
        >
          <div className="flex items-center truncate">
            <Menu className="mr-2 size-5" />
            <span className="truncate">
              {t(LOCIZE_DEALS_KEYS.dealsCategories)}
            </span>
          </div>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent
        className="h-[32rem] w-[17rem] overflow-y-auto "
        align="start"
      >
        <DropdownMenuGroup>
          <>
            <DropdownMenuItem
              className={cn(
                'flex cursor-pointer items-center justify-between px-3 py-3',
                !value && 'bg-accent',
              )}
              onSelect={() => {
                selectCategory(null);
              }}
            >
              <div className="flex items-center">
                <Tag className="mr-2 size-5 rotate-90" />
                <span>{t(LOCIZE_DEALS_KEYS.dealsAll)}</span>
              </div>
              {!value && !title && <Check className="size-4" />}
            </DropdownMenuItem>

            {categories.map((category) => {
              if (!category?.name) {
                return null;
              }

              if (!isDealCategory(category?.name)) {
                return null;
              }

              const categoryName = category.name;

              const isSelected = value === categoryName;
              const Icon = getDealCategoryIcon(categoryName);
              const translationKey =
                getDealCategoryTranslationKey(categoryName);
              return (
                <DropdownMenuItem
                  key={category?.name}
                  className={cn(
                    'flex cursor-pointer items-center justify-between px-3 py-3',
                    isSelected && 'bg-accent',
                  )}
                  onSelect={() => {
                    selectCategory(categoryName);
                  }}
                >
                  <div className="flex items-center">
                    <Icon className="mr-2 size-5" />
                    <span>{t(translationKey)}</span>
                  </div>
                  {isSelected && <Check className="size-4" />}
                </DropdownMenuItem>
              );
            })}

            {hasNextPage && <div ref={triggerRef} className="h-1" />}
          </>
        </DropdownMenuGroup>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};
