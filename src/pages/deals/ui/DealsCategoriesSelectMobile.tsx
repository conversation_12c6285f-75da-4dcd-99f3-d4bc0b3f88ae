import {
  Carousel,
  CarouselContent,
  CarouselItem,
} from '@components/ui/carousel';
import { LOCIZE_NAMESPACES } from '@config/locize';
import { useInfiniteCategories } from '@entities/deals/hooks/useInfiniteCategories';
import { useInfiniteScroll } from '@hooks/system/useInfiniteScroll';
import { getRouteApi } from '@tanstack/react-router';
import { useTranslation } from 'react-i18next';

import {
  getDealCategoryIcon,
  getDealCategoryTranslationKey,
  isDealCategory,
} from '../config';
import type { DealCategoryName } from '../types';
import { scrollToCategory } from '../utils';

const routeApi = getRouteApi('/_protected/_main/deals');

export const DealsCategoriesSelectMobile = () => {
  const navigate = routeApi.useNavigate();
  const {
    data: categories,
    isLoading,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
  } = useInfiniteCategories();
  const { t } = useTranslation(LOCIZE_NAMESPACES.deals);

  const { triggerRef } = useInfiniteScroll(fetchNextPage, {
    enabled: !isLoading,
    hasNextPage,
    isFetching: isFetchingNextPage,
    rootMargin: '100px',
  });

  if (!categories?.length) {
    return null;
  }

  const onCategoryClick = (category: DealCategoryName) => {
    navigate({
      search: {
        category: category,
      },
    });
    scrollToCategory(category, { isMobileView: true });
  };

  return (
    <Carousel
      opts={{ align: 'start', dragFree: true }}
      className="w-full overflow-hidden "
    >
      <CarouselContent isInteractive className="mr-5 ml-4 flex h-full py-5">
        {categories.map((category) => {
          if (!category) {
            return null;
          }

          if (!isDealCategory(category.name)) {
            return null;
          }

          const categoryName = category.name;
          const Icon = getDealCategoryIcon(category.name);
          const translationKey = getDealCategoryTranslationKey(categoryName);

          return (
            <CarouselItem
              key={`offer: ${category.name}`}
              className="w-fit basis-auto pl-2"
            >
              <button
                type="button"
                onClick={() => onCategoryClick(categoryName)}
                className="min-w-[8.5rem] cursor-pointer rounded-lg border border-gray-200 bg-white p-4 transition-shadow hover:border-gray-100 hover:bg-gray-100"
              >
                <div className="flex flex-col items-center justify-center">
                  <Icon className="h-5 w-5" />
                  <div className="flex flex-col items-center justify-center whitespace-nowrap pt-1.5">
                    {t(translationKey)}
                  </div>
                </div>
              </button>
            </CarouselItem>
          );
        })}

        {hasNextPage && (
          <CarouselItem className="w-fit basis-auto pl-2">
            <div ref={triggerRef} className="h-1 w-1" />
          </CarouselItem>
        )}
      </CarouselContent>
    </Carousel>
  );
};
