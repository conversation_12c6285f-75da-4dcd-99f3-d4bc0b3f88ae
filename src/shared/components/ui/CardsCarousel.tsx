import { Typography } from '@components/typography';
import {
  Carousel,
  type CarouselApi,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from '@components/ui/carousel';
import { useIsMobileView } from '@hooks/system';
import { cn } from '@utils/tailwind';
import { useEffect, useRef } from 'react';

type DealsFeatureCarouselProps<T extends { id?: string | number }> = {
  cards: T[];
  title: React.ReactNode;
  actionBtn?: React.ReactNode;
  renderCard: (card: T) => React.ReactNode;
  hasNextPage?: boolean;
  triggerRef?: (node: HTMLElement | null) => void;
  isFetchingNextPage?: boolean;
};

export const CardsCarousel = <T extends { id?: string | number }>({
  title,
  cards,
  actionBtn,
  renderCard,
  hasNextPage,
  triggerRef,
  isFetchingNextPage,
}: DealsFeatureCarouselProps<T>) => {
  const isMobileView = useIsMobileView();
  const carouselApiRef = useRef<CarouselApi>();
  const previousCardsLengthRef = useRef(cards.length);
  const preservedScrollPositionRef = useRef<number | null>(null);

  // Handle new items loading - preserve scroll position
  useEffect(() => {
    const api = carouselApiRef.current;
    if (!api) return;

    // If cards length increased (new items loaded)
    if (
      cards.length > previousCardsLengthRef.current &&
      preservedScrollPositionRef.current !== null
    ) {
      // Restore the exact scroll position
      const targetIndex = preservedScrollPositionRef.current;

      // Use setTimeout to ensure DOM is fully updated
      const restorePosition = () => {
        api.scrollTo(targetIndex, false);
        preservedScrollPositionRef.current = null;
      };

      setTimeout(restorePosition, 0);
    }

    previousCardsLengthRef.current = cards.length;
  }, [cards.length]);

  if (!cards?.length) {
    return null;
  }

  return (
    <Carousel
      className={'w-full overflow-hidden'}
      opts={{
        loop: true,
        align: 'start',
        dragFree: true,
      }}
      setApi={(api) => {
        carouselApiRef.current = api;
      }}
    >
      <div
        className={'mb-6 md:mb-2 grid grid-flow-col items-center px-6 md:px-0'}
      >
        <Typography
          tag="h2"
          variant={isMobileView ? 'xxs' : 'xs'}
          className={'flex items-center gap-3'}
        >
          {title}
        </Typography>

        <div className={'relative ml-auto h-8'}>
          {actionBtn}

          {!isMobileView && (
            <>
              <CarouselPrevious
                className={cn(
                  actionBtn ? '!-left-[5.7rem]' : '!-left-[4.7rem]',
                  '!absolute !top-0 !translate-y-0 border-none bg-neutral-50 p-1 disabled:hover:bg-neutral-50',
                )}
              />
              <CarouselNext
                className={cn(
                  actionBtn ? '!-left-[3rem]' : '!-left-[2rem]',
                  '!absolute !top-0 !translate-y-0 border-none bg-neutral-50 p-1 disabled:hover:bg-neutral-50',
                )}
              />
            </>
          )}
        </div>
      </div>
      <CarouselContent
        isInteractive
        containerClassName="pl-6 md:pl-0"
        className="-ml-6 mr-5 flex py-4"
      >
        {cards.map((card, index) => (
          <CarouselItem
            key={card.id ?? index}
            className="basis-[15.75rem] pl-6"
          >
            {renderCard(card)}
          </CarouselItem>
        ))}

        {hasNextPage && triggerRef && (
          <CarouselItem className="basis-auto pl-6">
            <div
              ref={(node) => {
                // Capture current scroll position before triggering load
                if (node && carouselApiRef.current) {
                  preservedScrollPositionRef.current =
                    carouselApiRef.current.selectedScrollSnap();
                }
                triggerRef(node);
              }}
              className="flex h-full w-16 items-center justify-center"
              style={{
                minWidth: '64px',
                visibility: 'visible',
                display: 'flex',
              }}
              data-testid="horizontal-infinite-scroll-trigger"
            >
              {isFetchingNextPage && (
                <div className="h-8 w-8 animate-spin rounded-full border-2 border-gray-300 border-t-gray-600" />
              )}
            </div>
          </CarouselItem>
        )}
      </CarouselContent>
    </Carousel>
  );
};
