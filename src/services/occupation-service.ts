import { OccupationCategory } from 'api/core/generated';
import { LOCIZE_INCOME_VERIFICATION_TRANSLATION_KEYS } from 'app-constants';
import type { TFunction } from 'i18next';
import type { Option } from 'models';

type GetOccupationCategoriesOptionsParams = {
  t: TFunction;
  occupationCategories: Array<string | null> | null | undefined;
};

export const getOccupationCategoriesOptions = ({
  t,
  occupationCategories,
}: GetOccupationCategoriesOptionsParams): Array<Option> =>
  (
    occupationCategories?.map(
      (categoryValue) =>
        ({
          value: categoryValue,
          label: t(
            `${
              LOCIZE_INCOME_VERIFICATION_TRANSLATION_KEYS.occupationCategoryPrefix
            }-${categoryValue?.replaceAll('_', '-').toLowerCase()}`,
          ),
        }) as Option,
    ) ?? []
  ).sort((a, b) => {
    if (typeof a.label !== 'string' || typeof b.label !== 'string') return 0;
    return a.label.localeCompare(b.label);
  });

export const occupationDocumentsByCategory: Record<
  OccupationCategory,
  string[]
> = {
  AUTHOR_SPORTSPERSON: [
    LOCIZE_INCOME_VERIFICATION_TRANSLATION_KEYS.authorSportsPersonDocument1,
    LOCIZE_INCOME_VERIFICATION_TRANSLATION_KEYS.authorSportsPersonDocument2,
  ],
  CLERIC: [LOCIZE_INCOME_VERIFICATION_TRANSLATION_KEYS.clericDocument1],
  DAILY_ALLOWANCE: [
    LOCIZE_INCOME_VERIFICATION_TRANSLATION_KEYS.dailyAllowanceDocument1,
  ],
  DISABILITY_ALLOWANCE: [
    LOCIZE_INCOME_VERIFICATION_TRANSLATION_KEYS.disabilityAllowanceDocument1,
    LOCIZE_INCOME_VERIFICATION_TRANSLATION_KEYS.disabilityAllowanceDocument2,
  ],
  EMPLOYEE: [LOCIZE_INCOME_VERIFICATION_TRANSLATION_KEYS.employeeDocument1],
  ENTREPRENEUR: [],
  FARMER: [
    LOCIZE_INCOME_VERIFICATION_TRANSLATION_KEYS.farmersDocument1,
    LOCIZE_INCOME_VERIFICATION_TRANSLATION_KEYS.farmersDocument2,
  ],
  FOREIGN_PENSIONER: [
    LOCIZE_INCOME_VERIFICATION_TRANSLATION_KEYS.foreignPensionerDocument1,
  ],
  LAWYER: [LOCIZE_INCOME_VERIFICATION_TRANSLATION_KEYS.lawyerDocument1],
  MATERNITY_ALLOWANCE: [
    LOCIZE_INCOME_VERIFICATION_TRANSLATION_KEYS.matemrityAllowanceDocument1,
  ],
  OFFICIAL: [
    LOCIZE_INCOME_VERIFICATION_TRANSLATION_KEYS.noAdditionalDocumentsNeeded,
  ],
  PENSIONER: [
    LOCIZE_INCOME_VERIFICATION_TRANSLATION_KEYS.noAdditionalDocumentsNeeded,
  ],
  PRIVATE_SECTOR_WORKER: [],
  PUBLIC_SECTOR_WORKER: [],
  RENT: [LOCIZE_INCOME_VERIFICATION_TRANSLATION_KEYS.rentDocument1],
  RETIRED: [],
  SEAFARER: [
    LOCIZE_INCOME_VERIFICATION_TRANSLATION_KEYS.seafarersDocument1,
    LOCIZE_INCOME_VERIFICATION_TRANSLATION_KEYS.seafarersDocument2,
  ],
  SELF_EMPLOYED: [
    LOCIZE_INCOME_VERIFICATION_TRANSLATION_KEYS.selfEmployedDocument1,
    LOCIZE_INCOME_VERIFICATION_TRANSLATION_KEYS.selfEmployedDocument2,
    LOCIZE_INCOME_VERIFICATION_TRANSLATION_KEYS.selfEmployedDocument3,
  ],
  SOLE_BUSINESS_OWNER: [
    LOCIZE_INCOME_VERIFICATION_TRANSLATION_KEYS.soleBusinessOwnerDocument1,
  ],
  STUDENT: [],
  UNDER_GOVERNMENT_ALLOWANCE: [],
  UNEMPLOYED: [],
  WORKING_ABROAD: [
    LOCIZE_INCOME_VERIFICATION_TRANSLATION_KEYS.workingAbroadDocument1,
  ],
  ARTISAN: [],
  CARETAKER: [],
  GOV_OR_SPECIALIST: [],
  TOP_MIDDLE_MGMT: [],
};

export const defaultUploadMaxSize = 10;

export enum OccupationCategoryNoAdditionalDocumentsNeeded {
  PENSIONER = OccupationCategory.PENSIONER,
  OFFICIAL = OccupationCategory.OFFICIAL,
}

export const isOccupationCategory = (
  occupation: string,
): occupation is keyof typeof occupationDocumentsByCategory => {
  return Object.keys(occupationDocumentsByCategory).includes(occupation);
};

export const isOccupationCategoryNoAdditionalDocumentsNeeded = (
  occupation: string,
): occupation is keyof typeof OccupationCategoryNoAdditionalDocumentsNeeded => {
  return Object.keys(OccupationCategoryNoAdditionalDocumentsNeeded).includes(
    occupation,
  );
};
